import { defineStore } from 'pinia'
import { ref } from 'vue'

// Mock API call - simulating network delay
const mockLoginApi = async (email, password) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (email === '<EMAIL>' && password === 'password') {
        resolve({
          user: {
            id: 1,
            email,
            name: 'Test User'
          },
          token: 'mock-jwt-token'
        })
      } else {
        reject(new Error('Invalid credentials'))
      }
    }, 1000)
  })
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const login = async (email, password) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await mockLoginApi(email, password)
      user.value = response.user
      token.value = response.token
      return true
    } catch (err) {
      error.value = err.message
      return false
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    token.value = null
  }

  return {
    user,
    token,
    loading,
    error,
    login,
    logout
  }
})