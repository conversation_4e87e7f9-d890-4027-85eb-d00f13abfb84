<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Logo/Brand Section -->
      <div>
        <h1 class="text-center text-4xl font-extrabold text-indigo-600">LIDEN</h1>
        <h2 class="mt-6 text-center text-3xl font-bold text-gray-900">Welcome back</h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Sign in to your account to continue
        </p>
      </div>

      <!-- Login Form -->
      <div class="mt-8 bg-white py-8 px-4 shadow-2xl sm:rounded-lg sm:px-10">
        <form class="space-y-6" @submit.prevent="handleLogin">
          <BaseInput
            id="email"
            label="Email address"
            v-model="email"
            type="email"
            icon="material-symbols:mail-outline"
            required
            placeholder="<EMAIL>"
          />

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="rememberMe"
                type="checkbox"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label for="remember-me" class="ml-2 block text-sm text-gray-900">Remember me</label>
            </div>

            <div class="text-sm">
              <a href="#" class="font-medium text-indigo-600 hover:text-indigo-500">Forgot password?</a>
            </div>
          </div>

          <div v-if="authStore.error" class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <Icon icon="material-symbols:error-outline" class="h-5 w-5 text-red-400" />
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-red-800">{{ authStore.error }}</p>
              </div>
            </div>
          </div>

          <div>
            <button
              type="submit"
              :disabled="authStore.loading"
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                <Icon 
                  icon="material-symbols:login" 
                  class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" 
                />
              </span>
              {{ authStore.loading ? 'Signing in...' : 'Sign in' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { Icon } from '@iconify/vue'

const router = useRouter()
const authStore = useAuthStore()
const email = ref('')
const password = ref('')

const handleLogin = async () => {
  try {
    const response = await authStore.login("<EMAIL>", "password")
    console.log(JSON.stringify(response))
    if (response) {
      router.push('/dashboard')
    } else {
      authStore.error = response.message || 'Login failed. Please try again.'
    }
  } catch (error) {
    authStore.error = error.message || 'An unexpected error occurred'
  }
}
</script>